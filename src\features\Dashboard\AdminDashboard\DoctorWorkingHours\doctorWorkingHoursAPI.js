import axios from 'axios';

// Base URL for the API
const API_BASE_URL = 'http://14.225.192.15:8080/api';

// Create axios instance for doctor working hours API
const doctorWorkingHoursAPI = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add request interceptor to include auth token if needed
doctorWorkingHoursAPI.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor for error handling
doctorWorkingHoursAPI.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('API Error:', error);
    return Promise.reject(error);
  }
);

/**
 * Get doctor working schedule by date
 * @param {string} date - Date in YYYY-MM-DD format
 * @returns {Promise} API response with doctor schedules
 */
export const getDoctorWorkingSchedule = async (date) => {
  try {
    const response = await doctorWorkingHoursAPI.get(`/schedules/doctors-working`, {
      params: { date }
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching doctor working schedule:', error);
    throw error;
  }
};

export default doctorWorkingHoursAPI;

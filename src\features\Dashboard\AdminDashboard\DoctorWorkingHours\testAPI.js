// Test file for Doctor Working Hours API
import { getDoctorWorkingSchedule } from './doctorWorkingHoursAPI';

// Test function to check API
export const testDoctorWorkingHoursAPI = async () => {
  try {
    console.log('Testing Doctor Working Hours API...');
    
    // Test with today's date
    const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
    console.log('Testing with date:', today);
    
    const response = await getDoctorWorkingSchedule(today);
    console.log('API Response:', response);
    
    if (Array.isArray(response)) {
      console.log(`Found ${response.length} doctor schedules`);
      response.forEach((doctorData, index) => {
        console.log(`Doctor ${index + 1}:`, {
          id: doctorData.doctor.id,
          email: doctorData.doctor.email,
          fullname: doctorData.doctor.fullname,
          slotsCount: doctorData.slots.length
        });
      });
    }
    
    return response;
  } catch (error) {
    console.error('API Test Error:', error);
    throw error;
  }
};

// Test with specific date (2025-07-30 as mentioned in the request)
export const testWithSpecificDate = async () => {
  try {
    console.log('Testing with specific date: 2025-07-30');
    const response = await getDoctorWorkingSchedule('2025-07-30');
    console.log('Specific Date Response:', response);
    return response;
  } catch (error) {
    console.error('Specific Date Test Error:', error);
    throw error;
  }
};

// Run tests if this file is executed directly
if (typeof window !== 'undefined') {
  // Browser environment
  window.testDoctorWorkingHoursAPI = testDoctorWorkingHoursAPI;
  window.testWithSpecificDate = testWithSpecificDate;
  
  console.log('Test functions available:');
  console.log('- testDoctorWorkingHoursAPI()');
  console.log('- testWithSpecificDate()');
}

import React, { useState, useEffect } from "react";
import {
  Card,
  DatePicker,
  Table,
  Button,
  Space,
  Tag,
  message,
  Spin,
  Typography,
  Row,
  Col,
} from "antd";
import {
  ReloadOutlined,
  CalendarOutlined,
  ClockCircleOutlined,
} from "@ant-design/icons";
import dayjs from "dayjs";
import api from "../../../../configs/api";
import "./DoctorWorkingHours.css";

const { Title, Text } = Typography;

const DoctorWorkingHours = () => {
  const [loading, setLoading] = useState(false);
  const [doctorSchedules, setDoctorSchedules] = useState([]);
  const [selectedDate, setSelectedDate] = useState(dayjs());

  // Fetch doctor working hours for selected date
  const fetchDoctorWorkingHours = async (date) => {
    setLoading(true);
    try {
      const formattedDate = date.format("YYYY-MM-DD");
      console.log("Fetching doctor schedules for date:", formattedDate);
      
      const response = await api.get(`/schedules/doctors-working`, {
        params: {
          date: formattedDate,
        },
      });

      console.log("Doctor schedules response:", response.data);
      setDoctorSchedules(response.data || []);
      message.success(`Đã tải lịch làm việc cho ngày ${formattedDate}`);
    } catch (error) {
      console.error("Error fetching doctor working hours:", error);
      message.error("Không thể tải lịch làm việc bác sĩ");
      setDoctorSchedules([]);
    } finally {
      setLoading(false);
    }
  };

  // Load data when component mounts or date changes
  useEffect(() => {
    fetchDoctorWorkingHours(selectedDate);
  }, [selectedDate]);

  // Handle date change
  const handleDateChange = (date) => {
    if (date) {
      setSelectedDate(date);
    }
  };

  // Handle refresh
  const handleRefresh = () => {
    fetchDoctorWorkingHours(selectedDate);
  };

  // Prepare table data
  const tableData = [];
  doctorSchedules.forEach((schedule, index) => {
    const doctor = schedule.doctor;
    const slots = schedule.slots || [];
    
    if (slots.length === 0) {
      // Doctor with no slots
      tableData.push({
        key: `${doctor.id}-no-slots`,
        doctorId: doctor.id,
        doctorName: doctor.fullname || doctor.email,
        doctorEmail: doctor.email,
        doctorRole: doctor.role,
        workDate: schedule.workDate,
        slotInfo: "Không có ca làm việc",
        startTime: "-",
        endTime: "-",
        maxBooking: "-",
        currentBooking: "-",
        availableBooking: "-",
        isNoSlot: true,
      });
    } else {
      // Doctor with slots
      slots.forEach((slot, slotIndex) => {
        tableData.push({
          key: `${doctor.id}-${slot.slotId}`,
          doctorId: doctor.id,
          doctorName: slotIndex === 0 ? (doctor.fullname || doctor.email) : "",
          doctorEmail: slotIndex === 0 ? doctor.email : "",
          doctorRole: slotIndex === 0 ? doctor.role : "",
          workDate: slotIndex === 0 ? schedule.workDate : "",
          slotInfo: `Ca ${slotIndex + 1}`,
          startTime: slot.startTime,
          endTime: slot.endTime,
          maxBooking: slot.maxBooking,
          currentBooking: slot.currentBooking,
          availableBooking: slot.availableBooking,
          isNoSlot: false,
        });
      });
    }
  });

  // Table columns
  const columns = [
    {
      title: "Bác sĩ",
      dataIndex: "doctorName",
      key: "doctorName",
      width: 200,
      render: (text, record) => (
        <div>
          {text && (
            <>
              <div style={{ fontWeight: "bold" }}>{text}</div>
              <div style={{ fontSize: "12px", color: "#666" }}>
                {record.doctorEmail}
              </div>
              <Tag color="blue">{record.doctorRole}</Tag>
            </>
          )}
        </div>
      ),
    },
    {
      title: "Ngày làm việc",
      dataIndex: "workDate",
      key: "workDate",
      width: 120,
      render: (text) => text && dayjs(text).format("DD/MM/YYYY"),
    },
    {
      title: "Ca làm việc",
      dataIndex: "slotInfo",
      key: "slotInfo",
      width: 100,
      render: (text, record) => (
        <Tag color={record.isNoSlot ? "red" : "green"}>{text}</Tag>
      ),
    },
    {
      title: "Giờ bắt đầu",
      dataIndex: "startTime",
      key: "startTime",
      width: 100,
      render: (text) => text !== "-" ? dayjs(text, "HH:mm:ss").format("HH:mm") : text,
    },
    {
      title: "Giờ kết thúc",
      dataIndex: "endTime",
      key: "endTime",
      width: 100,
      render: (text) => text !== "-" ? dayjs(text, "HH:mm:ss").format("HH:mm") : text,
    },
    {
      title: "Tối đa",
      dataIndex: "maxBooking",
      key: "maxBooking",
      width: 80,
      align: "center",
    },
    {
      title: "Đã đặt",
      dataIndex: "currentBooking",
      key: "currentBooking",
      width: 80,
      align: "center",
      render: (text, record) => (
        <Tag color={record.isNoSlot ? "default" : text > 0 ? "orange" : "green"}>
          {text}
        </Tag>
      ),
    },
    {
      title: "Còn trống",
      dataIndex: "availableBooking",
      key: "availableBooking",
      width: 80,
      align: "center",
      render: (text, record) => (
        <Tag color={record.isNoSlot ? "default" : text > 0 ? "green" : "red"}>
          {text}
        </Tag>
      ),
    },
  ];

  return (
    <Card
      title={
        <Space>
          <CalendarOutlined />
          <span>Quản lý Ca làm việc Bác sĩ</span>
        </Space>
      }
      extra={
        <Space>
          <DatePicker
            value={selectedDate}
            onChange={handleDateChange}
            format="DD/MM/YYYY"
            placeholder="Chọn ngày"
            allowClear={false}
          />
          <Button
            type="primary"
            icon={<ReloadOutlined />}
            onClick={handleRefresh}
            loading={loading}
          >
            Tải lại
          </Button>
        </Space>
      }
    >
      <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
        <Col span={24}>
          <div style={{ textAlign: "center" }}>
            <Title level={4}>
              <ClockCircleOutlined /> Lịch làm việc ngày{" "}
              {selectedDate.format("DD/MM/YYYY")}
            </Title>
            <Text type="secondary">
              Xem thông tin ca làm việc của tất cả bác sĩ trong ngày được chỉ định
            </Text>
          </div>
        </Col>
      </Row>

      <Spin spinning={loading}>
        <Table
          columns={columns}
          dataSource={tableData}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} của ${total} ca làm việc`,
          }}
          scroll={{ x: 800 }}
          size="middle"
          bordered
        />
      </Spin>

      {!loading && tableData.length === 0 && (
        <div style={{ textAlign: "center", padding: "40px 0" }}>
          <Text type="secondary">
            Không có dữ liệu ca làm việc cho ngày{" "}
            {selectedDate.format("DD/MM/YYYY")}
          </Text>
        </div>
      )}
    </Card>
  );
};

export default DoctorWorkingHours;

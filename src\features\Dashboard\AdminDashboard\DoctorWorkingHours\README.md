# Doctor Working Hours Management

## Tổng quan

Component "Quản lý Ca làm việc bác sĩ" cho phép admin xem lịch làm việc của tất cả bác sĩ theo ngày.

## Tính năng

- ✅ Chọn ngày để xem lịch làm việc
- ✅ Hiển thị danh sách bác sĩ dưới dạng dropdown/collapse
- ✅ Thông tin bác sĩ: tên, email, số điện thoại
- ✅ Chi tiết ca làm việc: thờ<PERSON> gian, số lượng tối đa, đã đặt, còn trống
- ✅ Giao diện responsive và thân thiện

## API Endpoint

```
GET /api/schedules/doctors-working?date=YYYY-MM-DD
```

### Request Parameters

- `date` (required): Ng<PERSON>y cần xem lịch làm việc (format: YYYY-MM-DD)

### Response Format

```json
[
  {
    "doctor": {
      "id": 7,
      "fullname": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> s<PERSON>",
      "email": "<EMAIL>",
      "phone": "0123456789",
      "imageUrl": "url_to_image",
      "role": "CONSULTANT"
    },
    "workDate": "2025-07-30",
    "slots": [
      {
        "slotId": 37,
        "date": "2025-07-30",
        "startTime": "07:00:00",
        "endTime": "08:30:00",
        "maxBooking": 4,
        "currentBooking": 0,
        "availableBooking": 4
      }
    ]
  }
]
```

## Cách sử dụng

### 1. Truy cập Admin Dashboard

- Đăng nhập với quyền admin
- Vào Admin Dashboard
- Chọn menu "Quản lý Ca làm việc bác sĩ"

### 2. Xem lịch làm việc

- Chọn ngày từ DatePicker (mặc định là hôm nay)
- Danh sách bác sĩ sẽ hiển thị dưới dạng collapse panels
- Click vào panel của bác sĩ để xem chi tiết ca làm việc

### 3. Thông tin hiển thị

- **Header của mỗi bác sĩ**: Avatar, tên, email, phone, tổng số ca, ca còn trống
- **Chi tiết ca làm việc**: Bảng hiển thị thời gian, số lượng tối đa, đã đặt, còn trống

## Files Structure

```
DoctorWorkingHours/
├── DoctorWorkingHours.jsx     # Main component
├── doctorWorkingHoursAPI.js   # API functions
├── index.js                   # Export file
├── testAPI.js                 # Test functions
└── README.md                  # Documentation
```

## Testing

Để test API, mở browser console và chạy:

```javascript
// Test với ngày hôm nay
testDoctorWorkingHoursAPI();

// Test với ngày cụ thể
testWithSpecificDate();
```

## Lưu ý

- Component sử dụng dayjs để xử lý ngày tháng
- API sử dụng config chung từ `src/configs/api.js` thay vì hardcode URL
- API endpoint cần authentication token (được xử lý tự động qua interceptor)
- Giao diện được thiết kế responsive cho mobile và desktop
- Sử dụng Ant Design components để đảm bảo consistency
